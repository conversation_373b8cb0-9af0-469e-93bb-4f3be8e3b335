import { Order } from '@/types/order';

/**
 * Configuration for multi-order export
 */
export interface MultiOrderExportConfig {
  selectedFields: string[];
  orders: Order[];
  sheetName: string;
  fileName: string;
}

/**
 * Header structure for Excel export
 */
export interface ExportHeaders {
  expandedHeaders: string[];
  subHeaders: string[];
}

/**
 * Column styling configuration
 */
export interface ColumnStyling {
  fieldValue: string;
  columnIndex: number;
  isProductDetail?: boolean;
  productColumnIndex?: number;
}
