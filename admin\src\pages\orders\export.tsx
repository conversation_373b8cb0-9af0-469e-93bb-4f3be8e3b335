import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input, Table, Spin, Pagination } from 'antd';
import FieldSelectionModal from '@/components/orders/Export/FieldSelectionModal';
import type { ColumnsType } from 'antd/es/table';
import { ArrowLeft, Search, FileDown } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { apiCall, endpoints } from '@/lib/api';
import { Order, OrderItem, PAYMENT_STATUS_OPTIONS, STATUS_OPTIONS } from '@/types/order'; // Keep STATUS_OPTIONS for table filters
import { formatCurrency } from '@/lib/utils';
import { useAuth } from '@/context/auth-hooks';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import { useToast } from '@/context/toast-hooks';
import { DateRangePickerWithPresets } from '@/components/common/DateRangePickerWithPresets';
import {
  DEFAULT_EXPORT_FIELDS,
  EXPORT_BUTTON_TEXTS,
  EXPORT_PAGE_TITLES,
  EXPORT_PLACEHOLDERS,
  EXPORT_TOAST_MESSAGES,
  formatDateForOrderDisplay,
  getOrderPaymentStatusLabel,
  getOrderStatusLabel,
  getShippingUnitLabel,
  ITEMS_PER_PAGE,
  EXCEL_EXPORT,
  SINGLE_ORDER_EXPORT_ITEM_COLUMNS,
  SINGLE_ORDER_EXPORT_SHOWROOM_ITEM_COLUMNS,
  SINGLE_ORDER_EXPORT_SUMMARY_LABELS,
  AVAILABLE_MULTI_ORDER_EXPORT_FIELDS,
  MULTI_ORDER_EXPORT_LAYOUT,
} from '@/constants/constants';
import {
  formatOrderItemsSummary,
  calculateSubtotal,
  formatTaxAsPercentage
} from '@/utils/multiOrderExportUtils';
import { NAVIGATION_PATHS } from '@/constants/navigationPaths';

const OrderExportPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showToast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<[string | null, string | null]>([null, null]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectAllFilteredActive, setSelectAllFilteredActive] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = ITEMS_PER_PAGE;
  const [isExportingMulti, setIsExportingMulti] = useState(false);

  const [isFieldSelectionModalVisible, setIsFieldSelectionModalVisible] = useState(false);
  const [selectedExportFields, setSelectedExportFields] = useState<string[]>(DEFAULT_EXPORT_FIELDS);

  useEffect(() => {
    if (selectedExportFields.length === 0 && DEFAULT_EXPORT_FIELDS.length > 0) {
       setSelectedExportFields(DEFAULT_EXPORT_FIELDS);
    }
  }, [selectedExportFields]); // Or [] if only on mount.

  const { data: ordersData, isLoading, error } = useQuery<{ results: Order[]; count: number }>({
    queryKey: ['ordersForExport', currentPage, searchTerm, dateRange, user?.id, user?.role],
    queryFn: async () => {
      let url = `${endpoints.orders.list}?page=${currentPage}&page_size=${pageSize}`;
      if (searchTerm) {
        // Assuming search by order ID or customer name for simplicity
        // Adjust search_by parameter as needed by your API
        url += `&search=${encodeURIComponent(searchTerm)}&search_by=customer_name_or_id`;
      }

      // Add date range parameters
      if (dateRange[0]) {
        url += `&date_from=${dateRange[0]}`;
      }
      if (dateRange[1]) {
        url += `&date_to=${dateRange[1]}`;
      }

      // Apply role-based filtering if necessary, similar to useTableOrders
      if (user?.role === "sales_admin" && user.id) {
         url = `${endpoints.orders.list}/by_sales_admin/?id=${user.id}&page=${currentPage}&page_size=${pageSize}`;
         if (searchTerm) {
           url += `&search=${encodeURIComponent(searchTerm)}&search_by=customer_name_or_id`;
         }
         // Add date range parameters for sales_admin
         if (dateRange[0]) {
           url += `&date_from=${dateRange[0]}`;
         }
         if (dateRange[1]) {
           url += `&date_to=${dateRange[1]}`;
         }
      }
      return apiCall('GET', url);
    },
    enabled: !!user, // Only run query if user is loaded
  });

  const orders = useMemo(() => ordersData?.results || [], [ordersData]);
  const totalOrders = useMemo(() => ordersData?.count || 0, [ordersData]);

  // Function to fetch all orders matching the current filters
  const fetchAllFilteredOrders = async (): Promise<Order[]> => {
    if (!user) return [];

    let allMatchingOrders: Order[] = [];
    let currentPageToFetch = 1;
    const fetchPageSize = 100; // Fetch in chunks of 100, or adjust as needed
    let hasMore = true;
    let totalFetchedCount = 0; // To avoid infinite loops if API count is off

    // Construct base URL similar to useQuery
    let baseUrl = `${endpoints.orders.list}?`;
    if (user.role === "sales_admin" && user.id) {
      baseUrl = `${endpoints.orders.list}/by_sales_admin/?id=${user.id}&`;
    }
    if (searchTerm) {
      baseUrl += `search=${encodeURIComponent(searchTerm)}&search_by=customer_name_or_id&`;
    }

    // Add date range parameters
    if (dateRange[0]) {
      baseUrl += `date_from=${dateRange[0]}&`;
    }
    if (dateRange[1]) {
      baseUrl += `date_to=${dateRange[1]}&`;
    }

    showToast(EXPORT_TOAST_MESSAGES.LOADING_ALL_ORDERS, "success");

    while (hasMore) {
      const url = `${baseUrl}page=${currentPageToFetch}&page_size=${fetchPageSize}`;
      try {
        const response = await apiCall<{ results: Order[]; count?: number }>('GET', url);
        if (response.results && response.results.length > 0) {
          allMatchingOrders = allMatchingOrders.concat(response.results);
          totalFetchedCount += response.results.length;
        }
        if (response.results && response.results.length > 0) {
          currentPageToFetch++;

          // Safety break:
          // if (currentPageToFetch > 50) {
          //   showToast(EXPORT_TOAST_MESSAGES.FETCH_LIMIT_REACHED, "error");
          //   hasMore = false;
          // }
        } else {
          hasMore = false;
        }
      } catch (fetchError: any) {
        if (fetchError?.response?.status === 404) {
          hasMore = false;
        } else {
          console.error("Error fetching all filtered orders:", fetchError);
          showToast(EXPORT_TOAST_MESSAGES.FETCH_ALL_ORDERS_ERROR, "error");
          hasMore = false;
          return [];
        }
      }
    }
    if (allMatchingOrders.length > 0) {
        showToast(EXPORT_TOAST_MESSAGES.ORDERS_LOADED(allMatchingOrders.length), "success");
    } else {
        showToast(EXPORT_TOAST_MESSAGES.NO_ORDERS_FOUND_FILTER, "success");
    }
    return allMatchingOrders;
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page on new search
  };

  // Reset to first page when date range changes
  const handleDateRangeChange = (dates: [string | null | undefined, string | null | undefined]) => {
    setDateRange([
      dates[0] || null,
      dates[1] || null
    ]);
    setCurrentPage(1); // Reset to first page on date range change
  };

  // Handler for when individual row selections change OR table header checkbox is used
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectAllFilteredActive(false); // Any individual selection change deactivates "select all filtered" mode
  };

  const rowSelection = {
    // If selectAllFilteredActive is true, all items on the current page should appear selected
    selectedRowKeys: selectAllFilteredActive ? orders.map(o => o.id) : selectedRowKeys,
    onChange: onSelectChange,
  };

  // Renamed from handleSelectAll, now specifically for "select all filtered"
  const handleToggleSelectAllFiltered = () => {
    if (selectAllFilteredActive) {
      setSelectedRowKeys([]);
      setSelectAllFilteredActive(false);
    } else {
      setSelectedRowKeys(orders.map(order => order.id)); // Select current page for visual cue
      setSelectAllFilteredActive(true);
    }
  };



  const handleProceedWithMultiOrderExport = async () => {
    if (selectedExportFields.length === 0) {
      showToast(EXPORT_TOAST_MESSAGES.SELECT_AT_LEAST_ONE_FIELD, "error");
      return;
    }
    setIsExportingMulti(true);
    showToast(EXPORT_TOAST_MESSAGES.PREPARING_EXCEL, "success");

    let ordersToExportInTable: Order[] = [];
    if (selectAllFilteredActive) {
      // Fetch all orders that match the current filter criteria
      ordersToExportInTable = await fetchAllFilteredOrders();
    } else {
      // Export only the selected orders from the current page
      ordersToExportInTable = orders.filter(order => selectedRowKeys.includes(order.id));
    }

    if (ordersToExportInTable.length === 0) {
      showToast(EXPORT_TOAST_MESSAGES.NO_ORDERS_SELECTED_FOR_EXPORT, "error");
      setIsExportingMulti(false);
      setIsFieldSelectionModalVisible(false);
      return;
    }

    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet(EXCEL_EXPORT.MULTI_ORDER_SHEET_NAME);

    // Create headers with product detail expansion
    const expandedHeaders: string[] = [];
    const subHeaders: string[] = [];

    selectedExportFields.forEach(fieldValue => {
      const field = AVAILABLE_MULTI_ORDER_EXPORT_FIELDS.find(f => f.value === fieldValue);
      const fieldLabel = field ? field.label : fieldValue;

      if (fieldValue === 'items_detail') {
        const productColumns = ['Mã hàng', 'Sản phẩm', 'Số lượng', 'Đơn giá', 'Thành tiền'];

        expandedHeaders.push(fieldLabel);
        for (let i = 1; i < productColumns.length; i++) {
          expandedHeaders.push('');
        }

        subHeaders.push(...productColumns);
      } else {
        expandedHeaders.push(fieldLabel);
        subHeaders.push(''); 
      }
    });

    const headerRow = worksheet.addRow(expandedHeaders);
    headerRow.font = { bold: true };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
    headerRow.height = 25; 

    const subHeaderRow = worksheet.addRow(subHeaders);
    subHeaderRow.font = { bold: true };
    subHeaderRow.alignment = { horizontal: 'center', vertical: 'middle' };
    subHeaderRow.height = 25;

    let currentCol = 1;
    selectedExportFields.forEach(fieldValue => {
      if (fieldValue === 'items_detail') {
        const productColumnCount = 5; 

        worksheet.mergeCells(1, currentCol, 1, currentCol + productColumnCount - 1);
        currentCol += productColumnCount;
      } else {
        worksheet.mergeCells(1, currentCol, 2, currentCol);
        currentCol += 1;
      }
    });

    console.log(ordersToExportInTable)

    // Add Data Rows
    ordersToExportInTable.forEach(order => {
      const orderItems = order.items || [];
      const itemCount = Math.max(orderItems.length, 1); // At least 1 row per order

      // Create rows for this order (one per product item)
      for (let itemIndex = 0; itemIndex < itemCount; itemIndex++) {
        const currentItem = orderItems[itemIndex];
        const isFirstItemRow = itemIndex === 0;

        const rowData: any[] = [];

        selectedExportFields.forEach(fieldValue => {
          if (fieldValue === 'items_detail') {
            // Add product detail columns (always 5 columns)
            if (currentItem) {
              const productName = currentItem.variant_name
                ? `${currentItem.product_name} (${currentItem.variant_name})`
                : currentItem.product_name;

              if (order.is_showroom) {
                // For showroom orders: show code, name with weight info, quantity, leave price columns empty
                const productNameWithWeight = currentItem.product_weight
                  ? `${productName} (${currentItem.product_weight}kg)`
                  : productName;

                rowData.push(
                  currentItem.product_code || 'N/A',
                  productNameWithWeight,
                  currentItem.quantity,
                  'Showroom', // Indicate this is a showroom order
                  ''  // Empty total column
                );
              } else {
                // For regular orders: show all columns
                rowData.push(
                  currentItem.product_code || 'N/A',
                  productName,
                  currentItem.quantity,
                  currentItem.price,
                  currentItem.total_price
                );
              }
            } else {
              // Empty product columns for orders with no items (always 5 columns)
              for (let i = 0; i < 5; i++) {
                rowData.push('');
              }
            }
          } else {
            // Add order information only on the first row
            if (isFirstItemRow) {
              switch (fieldValue) {
                case 'id':
                  rowData.push(order.id);
                  break;
                case 'created_at':
                  rowData.push(formatDateForOrderDisplay(order.created_at));
                  break;
                case 'delivery_date':
                  rowData.push(order.delivery_date ? formatDateForOrderDisplay(order.delivery_date) : '');
                  break;
                case 'customer_name':
                  rowData.push(order.user?.full_name || 'N/A');
                  break;
                case 'phone_number':
                  rowData.push(order.phone_number);
                  break;
                case 'shipping_address':
                  rowData.push(`${order.shipping_address}, Phường ${order.ward}, Quận ${order.district}, Thành phố ${order.city}`);
                  break;
                case 'items_summary':
                  rowData.push(formatOrderItemsSummary(order.items));
                  break;
                case 'payment_method':
                  rowData.push(order.payment_method || '');
                  break;
                case 'subtotal':
                  rowData.push(calculateSubtotal(order.items));
                  break;
                case 'shipping_fee':
                  rowData.push(order.shipping_fee || 0);
                  break;
                case 'discount':
                  rowData.push(order.discount || 0);
                  break;
                case 'tax':
                  rowData.push(formatTaxAsPercentage(order.tax));
                  break;
                case 'final_total':
                  rowData.push(order.final_total);
                  break;
                case 'status':
                  rowData.push(getOrderStatusLabel(order.status));
                  break;
                case 'payment_status':
                  rowData.push(getOrderPaymentStatusLabel(order.payment_status));
                  break;
                case 'notes':
                  rowData.push(order.notes);
                  break;
                case 'email':
                  rowData.push(order.email || '');
                  break;
                case 'ward':
                  rowData.push(order.ward || '');
                  break;
                case 'district':
                  rowData.push(order.district || '');
                  break;
                case 'city':
                  rowData.push(order.city || '');
                  break;
                case 'sales_admin':
                  rowData.push(order.sales_admin ? `${order.sales_admin.first_name} ${order.sales_admin.last_name}`.trim() : '');
                  break;
                case 'delivery_staff':
                  rowData.push(order.delivery_staff ? `${order.delivery_staff.first_name} ${order.delivery_staff.last_name}`.trim() : '');
                  break;
                case 'confirmation_time':
                  rowData.push(order.confirmation_time ? formatDateForOrderDisplay(order.confirmation_time) : '');
                  break;
                case 'completion_time':
                  rowData.push(order.completion_time ? formatDateForOrderDisplay(order.completion_time) : '');
                  break;
                case 'shipping_unit':
                  rowData.push(getShippingUnitLabel(order.shipping_unit));
                  break;
                default:
                  rowData.push('');
                  break;
              }
            } else {
              // Empty cell for subsequent item rows
              rowData.push('');
            }
          }
        });

        worksheet.addRow(rowData);
      }

      // Merge cells for order information that spans multiple item rows
      if (itemCount > 1) {
        const currentRowNumber = worksheet.lastRow!.number;
        const startRowNumber = currentRowNumber - itemCount + 1;

        let colIndex = 1;
        selectedExportFields.forEach(fieldValue => {
          if (fieldValue !== 'items_detail') {
            // Merge this column across all item rows for this order
            worksheet.mergeCells(startRowNumber, colIndex, currentRowNumber, colIndex);
            colIndex += 1;
          } else {
            // Skip product detail columns (don't merge) - always 5 columns
            colIndex += 5;
          }
        });
      }
    });

    // Style columns based on field types
    let currentColIndex = 1;
    selectedExportFields.forEach((fieldValue) => {
        if (fieldValue === 'items_detail') {
          // Handle product detail columns (always 5 columns)
          const productColumnCount = 5;

          for (let i = 0; i < productColumnCount; i++) {
            const column = worksheet.getColumn(currentColIndex + i);

            if (i === 0) { // Product code column
              column.width = 12;
            } else if (i === 1) { // Product name column
              column.width = 30;
            } else if (i === 2) { // Quantity column
              column.width = 10;
              column.alignment = { horizontal: 'right' };
            } else if (i === 3) { // Unit price column
              column.width = 15;
              column.alignment = { horizontal: 'right' };
              column.numFmt = '#,##0';
            } else if (i === 4) { // Total price column
              column.width = 15;
              column.alignment = { horizontal: 'right' };
              column.numFmt = '#,##0';
            }
          }

          currentColIndex += productColumnCount;
        } else {
          // Handle regular columns
          const column = worksheet.getColumn(currentColIndex);

          switch (fieldValue) {
            case 'subtotal':
            case 'final_total':
              column.numFmt = '#,##0';
              column.alignment = { horizontal: 'right' };
              column.width = 15;
              break;
            case 'shipping_fee':
            case 'discount':
              column.numFmt = '#,##0 "₫"';
              column.alignment = { horizontal: 'right' };
              column.width = 15;
              break;
            case 'tax':
              column.numFmt = '0.00"%"';
              column.alignment = { horizontal: 'right' };
              column.width = 12;
              break;
            case 'items_summary':
              column.width = 40;
              column.alignment = { wrapText: true, vertical: 'top' };
              break;
            case 'customer_name':
              column.width = 20;
              break;
            case 'shipping_address':
              column.width = 35;
              column.alignment = { wrapText: true, vertical: 'top' };
              break;
            default:
              // Auto-fit other columns with reasonable limits
              let maxLength = 0;
              column.eachCell!({ includeEmpty: false }, cell => {
                  const columnLength = cell.value ? cell.value.toString().length : 10;
                  if (columnLength > maxLength) {
                      maxLength = columnLength;
                  }
              });
              column.width = Math.max(
                MULTI_ORDER_EXPORT_LAYOUT.MIN_COLUMN_WIDTH,
                Math.min(maxLength + 2, MULTI_ORDER_EXPORT_LAYOUT.MAX_COLUMN_WIDTH)
              );
              break;
          }

          currentColIndex += 1;
        }
    });

    // Add borders and styling to all data cells
    const totalRows = worksheet.lastRow!.number;
    const totalCols = currentColIndex - 1;

    for (let row = 1; row <= totalRows; row++) {
      for (let col = 1; col <= totalCols; col++) {
        const cell = worksheet.getCell(row, col);

        // Add borders to all cells
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };

        // Set vertical alignment for all cells
        if (!cell.alignment) {
          cell.alignment = { vertical: 'top' };
        } else {
          cell.alignment = { ...cell.alignment, vertical: 'top' };
        }
      }
    }

    // Style header rows
    for (let col = 1; col <= totalCols; col++) {
      const headerCell1 = worksheet.getCell(1, col);
      const headerCell2 = worksheet.getCell(2, col);

      // Style main header row
      headerCell1.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6E6FA' }
      };
      headerCell1.alignment = {
        horizontal: 'center',
        vertical: 'middle'
      };
      headerCell1.font = { bold: true };

      // Style sub-header row
      headerCell2.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFF0F8FF' }
      };
      headerCell2.alignment = {
        horizontal: 'center',
        vertical: 'middle'
      };
      headerCell2.font = { bold: true };
    }

    try {
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: EXCEL_EXPORT.FILE_TYPE });
      const fileName = `${EXCEL_EXPORT.MULTI_ORDER_FILE_NAME_PREFIX}${new Date().toISOString().slice(0,10)}.xlsx`;
      saveAs(blob, fileName);
      showToast(EXPORT_TOAST_MESSAGES.EXPORT_SUCCESS, "success");
    } catch (err) {
      console.error('Lỗi khi tạo file XLSX cho nhiều đơn hàng:', err);
      showToast(EXPORT_TOAST_MESSAGES.EXPORT_ERROR, "error");
    } finally {
      setIsExportingMulti(false);
      setIsFieldSelectionModalVisible(false);
    }
  };

  const handleExportSelected = async () => {
    if (selectAllFilteredActive) {
      // TODO: Implement actual XLSX export logic for ALL filtered orders.
      // This will require fetching all pages of data matching `searchTerm` and other filters.
      if (totalOrders > 0) {
        setIsFieldSelectionModalVisible(true);
      } else {
        showToast(EXPORT_TOAST_MESSAGES.NO_ORDERS_MATCH_FILTER_TO_EXPORT, "error");
      }
    } else {
      if (selectedRowKeys.length === 0) {
        showToast(EXPORT_TOAST_MESSAGES.SELECT_AT_LEAST_ONE_ORDER, "error");
        return;
      }

      if (selectedRowKeys.length === 1) {
        const orderIdToExport = selectedRowKeys[0];
        const orderToExport = orders.find(order => order.id === orderIdToExport);

        if (!orderToExport) {
          showToast(EXPORT_TOAST_MESSAGES.SELECTED_ORDER_NOT_FOUND, "error");
          return;
        }

        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet(`${EXCEL_EXPORT.SINGLE_ORDER_SHEET_NAME_PREFIX}${orderToExport.id}`);

        // Using direct strings since we don't have header labels defined in constants
        worksheet.addRow([`Mã Đơn Hàng: ${orderToExport.id}`]);
        worksheet.addRow([`Khách hàng: ${orderToExport.user?.full_name || 'N/A'}`]);
        worksheet.addRow([`Ngày tạo: ${formatDateForOrderDisplay(orderToExport.created_at)}`]);
        if (orderToExport.delivery_date) {
          worksheet.addRow([`Ngày phải giao: ${formatDateForOrderDisplay(orderToExport.delivery_date)}`]);
        }
        worksheet.addRow([]);

        // Use different columns for showroom vs regular orders
        const isShowroomOrder = orderToExport.is_showroom;
        const itemColumns = isShowroomOrder ? SINGLE_ORDER_EXPORT_SHOWROOM_ITEM_COLUMNS : SINGLE_ORDER_EXPORT_ITEM_COLUMNS;

        worksheet.addRow(itemColumns);
        worksheet.getRow(worksheet.lastRow!.number).font = { bold: true };

        // Item Table Data
        orderToExport.items.forEach((item: OrderItem) => {
          if (isShowroomOrder) {
            // For showroom orders: Product Code, Product, Quantity, Weight
            worksheet.addRow([
              item.product_code || 'N/A',
              item.variant_name ? `${item.product_name} (${item.variant_name})` : item.product_name,
              item.quantity,
              item.product_weight || 'N/A',
            ]);
          } else {
            // For regular orders: Product Code, Product, Quantity, Unit Price, Total Price
            worksheet.addRow([
              item.product_code || 'N/A',
              item.variant_name ? `${item.product_name} (${item.variant_name})` : item.product_name,
              item.quantity,
              item.price,
              item.total_price,
            ]);
          }
        });

        // Style numeric columns (optional)
        worksheet.getColumn('C').alignment = { horizontal: 'right' }; // Quantity column
        if (isShowroomOrder) {
          // For showroom orders: Column D is weight
          worksheet.getColumn('D').alignment = { horizontal: 'right' };
          worksheet.getColumn('D').numFmt = EXCEL_EXPORT.NUMBER_FORMAT;
        } else {
          // For regular orders: Column D is price, Column E is total price
          worksheet.getColumn('D').numFmt = EXCEL_EXPORT.CURRENCY_FORMAT;
          worksheet.getColumn('D').alignment = { horizontal: 'right' };
          worksheet.getColumn('E').numFmt = EXCEL_EXPORT.CURRENCY_FORMAT;
          worksheet.getColumn('E').alignment = { horizontal: 'right' };
        }


        // Empty row for spacing
        worksheet.addRow([]);

        // Summary Section - only for non-showroom orders
        if (!isShowroomOrder) {
          // Ensure all item total_prices are numbers
          const subtotal = orderToExport.items.reduce((sum, item) => sum + Number(item.total_price || 0), 0);

          const shippingFee = Number(orderToExport.shipping_fee || 0);
          const discount = Number(orderToExport.discount || 0);
          const taxRate = Number(orderToExport.tax || 0); // This is a rate like 0.1 for 10%

          worksheet.addRow([SINGLE_ORDER_EXPORT_SUMMARY_LABELS.SUBTOTAL, '', '', '', subtotal]);
          worksheet.addRow([SINGLE_ORDER_EXPORT_SUMMARY_LABELS.SHIPPING_FEE, '', '', '', shippingFee]);
          worksheet.addRow([SINGLE_ORDER_EXPORT_SUMMARY_LABELS.DISCOUNT, '', '', '', discount]);
          worksheet.addRow([SINGLE_ORDER_EXPORT_SUMMARY_LABELS.TAX_PERCENTAGE, '', '', '', taxRate * 100]);
          const totalBeforeTax = subtotal + shippingFee - discount;
          const calculatedFinalTotal = Math.round(totalBeforeTax * (1 + taxRate));

          worksheet.addRow([SINGLE_ORDER_EXPORT_SUMMARY_LABELS.TOTAL, '', '', '', calculatedFinalTotal]).font = { bold: true };

          // Style summary values
          const summaryStartRow = worksheet.lastRow!.number - 4; // Tạm tính is the first of these 5 rows
          for (let i = 0; i < 5; i++) { // Iterate 5 times for Tạm tính, Phí GH, Giảm giá, Thuế, Tổng cộng
              const labelCell = worksheet.getCell(`A${summaryStartRow + i}`);
              const valueCell = worksheet.getCell(`E${summaryStartRow + i}`);

              valueCell.numFmt = EXCEL_EXPORT.CURRENCY_FORMAT;
              valueCell.alignment = { horizontal: 'right' };

              if (i === 3) { // Tax percentage row
                  valueCell.numFmt = EXCEL_EXPORT.PERCENTAGE_FORMAT;
                  valueCell.numFmt = EXCEL_EXPORT.NUMBER_FORMAT;
              }

              if (i === 4) { // Total row
                   labelCell.font = { bold: true };
                   valueCell.font = { bold: true };
              }
          }
          // Re-adjusting tax row to show percentage value correctly
          worksheet.getCell(`E${summaryStartRow + 3}`).numFmt = EXCEL_EXPORT.NUMBER_FORMAT;
          worksheet.getCell(`E${summaryStartRow + 3}`).value = taxRate * 100;

          // Apply currency format to all monetary values
          worksheet.getCell(`E${summaryStartRow + 0}`).numFmt = EXCEL_EXPORT.CURRENCY_FORMAT; // Subtotal
          worksheet.getCell(`E${summaryStartRow + 1}`).numFmt = EXCEL_EXPORT.CURRENCY_FORMAT; // Shipping fee
          worksheet.getCell(`E${summaryStartRow + 2}`).numFmt = EXCEL_EXPORT.CURRENCY_FORMAT; // Discount
          worksheet.getCell(`E${summaryStartRow + 4}`).numFmt = EXCEL_EXPORT.CURRENCY_FORMAT; // Total
        }


        // Auto-fit columns (optional, can be resource-intensive for large sheets)
        worksheet.columns.forEach(column => {
            let maxLength = 0;
            column.eachCell!({ includeEmpty: false }, cell => {
                let columnLength = cell.value ? cell.value.toString().length : 10;
                if (columnLength > maxLength) {
                    maxLength = columnLength;
                }
            });
            column.width = maxLength < 10 ? 10 : Math.min(maxLength + 2, 50);
        });

        // Generate and Download XLSX
        try {
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], { type: EXCEL_EXPORT.FILE_TYPE });
            const fileName = `${EXCEL_EXPORT.SINGLE_ORDER_FILE_NAME_PREFIX}${orderToExport.id}.xlsx`;
            saveAs(blob, fileName);
            showToast(EXPORT_TOAST_MESSAGES.SINGLE_ORDER_EXPORT_SUCCESS, "success");
        } catch (err) {
            console.error('Lỗi khi tạo file XLSX:', err);
            showToast(EXPORT_TOAST_MESSAGES.EXPORT_ERROR, "error");
        }

      } else { // selectedRowKeys.length > 1
        setIsFieldSelectionModalVisible(true); // Show modal for field selection
      }
    }
  };

  const columns: ColumnsType<Order> = [
    {
      title: 'Mã ĐH',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => formatDateForOrderDisplay(text),
      width: 120,
      sorter: (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    },
    {
      title: 'Ngày phải giao',
      dataIndex: 'delivery_date',
      key: 'delivery_date',
      render: (text: string | null) => text ? formatDateForOrderDisplay(text) : '—',
      width: 140,
      sorter: (a, b) => {
        if (!a.delivery_date && !b.delivery_date) return 0;
        if (!a.delivery_date) return 1;
        if (!b.delivery_date) return -1;
        return new Date(a.delivery_date).getTime() - new Date(b.delivery_date).getTime();
      },
    },
    {
      title: 'Khách hàng',
      key: 'customer',
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.user?.full_name || 'N/A'}</div> {/* Use optional chaining and fallback for user */}
          <div className="text-sm text-muted-foreground">{record.phone_number}</div>
        </div>
      ),
      sorter: (a, b) => (a.user?.full_name || '').localeCompare(b.user?.full_name || ''),
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'final_total',
      key: 'final_total',
      render: (text: number) => formatCurrency(text),
      width: 150,
      sorter: (a, b) => a.final_total - b.final_total,
    },
    {
      title: 'Trạng thái ĐH',
      dataIndex: 'status',
      key: 'status',
      render: (status: Order['status']) => getOrderStatusLabel(status), // Use Order['status']
      width: 150,
      filters: STATUS_OPTIONS.map(s => ({text: s.label, value: s.value})),
      onFilter: (value, record) => record.status === value,
    },
    {
      title: 'Trạng thái TT',
      dataIndex: 'payment_status',
      key: 'payment_status',
      render: (status: Order['payment_status']) => getOrderPaymentStatusLabel(status),
      width: 150,
      filters: PAYMENT_STATUS_OPTIONS.map(s => ({text: s.label, value: s.value})),
      onFilter: (value, record) => record.payment_status === value,
    }
  ];

  if (error) {
    return (
      <div className="container mx-auto py-6 text-red-500">
        Lỗi khi tải danh sách đơn hàng: {error.message}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-4 flex items-center justify-between">
        <Button variant="outline" onClick={() => navigate(NAVIGATION_PATHS.ORDERS)}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {EXPORT_BUTTON_TEXTS.BACK_TO_ORDERS}
        </Button>
        <h1 className="text-2xl font-bold">{EXPORT_PAGE_TITLES.EXPORT_ORDERS_PAGE}</h1>
      </div>

      <div className="mb-4 p-4 border rounded-md bg-background shadow">
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <Input
            placeholder={EXPORT_PLACEHOLDERS.SEARCH_ORDERS}
            prefix={<Search className="h-4 w-4 text-muted-foreground" />}
            value={searchTerm}
            onChange={handleSearch}
            className="flex-grow"
          />
          <div className="min-w-[300px]">
            <DateRangePickerWithPresets
              value={dateRange}
              onChange={handleDateRangeChange}
              className="w-full"
            />
          </div>
        </div>
        <div className="flex flex-wrap gap-2 mb-4 items-center">
          <Button
            onClick={handleToggleSelectAllFiltered}
            disabled={isLoading || totalOrders === 0}
            variant={selectAllFilteredActive ? "destructive" : "outline"}
          >
            {selectAllFilteredActive
              ? EXPORT_BUTTON_TEXTS.DESELECT_ALL_FILTERED(totalOrders)
              : EXPORT_BUTTON_TEXTS.SELECT_ALL_FILTERED(totalOrders)}
          </Button>
          <Button
            onClick={handleExportSelected}
            disabled={isLoading || (selectedRowKeys.length === 0 && !selectAllFilteredActive)}
            variant="default"
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <FileDown className="mr-2 h-4 w-4" />
            {selectAllFilteredActive
              ? EXPORT_BUTTON_TEXTS.EXPORT_ALL_FILTERED(totalOrders)
              : EXPORT_BUTTON_TEXTS.EXPORT_SELECTED(selectedRowKeys.length)}
          </Button>
        </div>
      </div>

      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      )}

      {!isLoading && orders.length === 0 && searchTerm && (
         <div className="text-center py-8 text-muted-foreground">
           {EXPORT_TOAST_MESSAGES.NO_ORDERS_MATCH_SEARCH(searchTerm)}
         </div>
      )}
       {!isLoading && orders.length === 0 && !searchTerm && (
         <div className="text-center py-8 text-muted-foreground">
           {EXPORT_TOAST_MESSAGES.NO_ORDERS_TO_DISPLAY}
         </div>
      )}

      {!isLoading && orders.length > 0 && (
        <>
          <Table
            rowKey="id"
            rowSelection={rowSelection}
            columns={columns}
            dataSource={orders}
            pagination={false} // Using custom pagination
            scroll={{ x: 'max-content' }}
            className="bg-background shadow rounded-md"
          />
          {totalOrders > pageSize && (
            <div className="flex justify-center mt-4">
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={totalOrders}
                onChange={(page) => setCurrentPage(page)}
                showSizeChanger={false}
              />
            </div>
          )}
        </>
      )}
      <FieldSelectionModal
        visible={isFieldSelectionModalVisible}
        onOk={handleProceedWithMultiOrderExport}
        onCancel={() => setIsFieldSelectionModalVisible(false)}
        selectedFields={selectedExportFields}
        onFieldsChange={setSelectedExportFields}
        isExporting={isExportingMulti}
      />
    </div>
  );
};

export default OrderExportPage;
