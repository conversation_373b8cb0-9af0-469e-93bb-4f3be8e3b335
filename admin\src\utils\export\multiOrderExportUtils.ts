import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import { EXCEL_EXPORT } from '@/constants/constants';
import { MultiOrderExportConfig } from './types';
import { createExportHeaders, addHeadersToWorksheet, mergeHeaderCells } from './headerUtils';
import { addDataRowsToWorksheet } from './rowUtils';
import { applyColumnStyling, applyBordersAndStyling, styleHeaderRows } from './stylingUtils';

/**
 * Main function to handle multi-order export to Excel
 */
export const exportMultipleOrdersToExcel = async (config: MultiOrderExportConfig): Promise<void> => {
  const { selectedFields, orders, sheetName, fileName } = config;
  
  // Create workbook and worksheet
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet(sheetName);
  
  // Create and add headers
  const headers = createExportHeaders(selectedFields);
  addHeadersToWorksheet(worksheet, headers);
  mergeHeaderCells(worksheet, selectedFields);
  
  // Add data rows
  addDataRowsToWorksheet(worksheet, orders, selectedFields);
  
  // Apply styling
  const totalCols = applyColumnStyling(worksheet, selectedFields);
  applyBordersAndStyling(worksheet, totalCols);
  styleHeaderRows(worksheet, totalCols);
  
  // Generate and download file
  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], { type: EXCEL_EXPORT.FILE_TYPE });
  saveAs(blob, fileName);
};

/**
 * Creates export configuration for multi-order export
 */
export const createMultiOrderExportConfig = (
  selectedFields: string[],
  orders: any[],
  customFileName?: string
): MultiOrderExportConfig => {
  const fileName = customFileName || 
    `${EXCEL_EXPORT.MULTI_ORDER_FILE_NAME_PREFIX}${new Date().toISOString().slice(0,10)}.xlsx`;
  
  return {
    selectedFields,
    orders,
    sheetName: EXCEL_EXPORT.MULTI_ORDER_SHEET_NAME,
    fileName
  };
};
