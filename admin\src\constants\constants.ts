import { Order, PAYMENT_STATUS_OPTIONS, STATUS_OPTIONS, SHIPPING_UNIT_OPTIONS } from '@/types/order';

export const EXPECTED_COLUMNS = [
    "Tr<PERSON>ng thái cập nh<PERSON>t",
    "<PERSON><PERSON> và Tên",
    "Số Điện Thoại",
    "<PERSON><PERSON><PERSON><PERSON>",
    "Ph<PERSON><PERSON><PERSON>",
    "Quận",
    "Tỉnh/Thành phố",
    "Ngày Order",
    "NVBH",
    "Sản Phẩm",
    "<PERSON><PERSON><PERSON> T<PERSON>ề<PERSON>",
    "<PERSON><PERSON> <PERSON>",
    "<PERSON><PERSON><PERSON>",
    "Phương Thứ<PERSON> Toán",
    "<PERSON><PERSON><PERSON> G<PERSON>",
    "Đơn Vị Vận Chu<PERSON>ển",
    "Trạng Thái Đơn Hàng",
    "Nhân viên giao hàng",
    "Note",
];

export const ITEMS_PER_PAGE = 12;

/**
 * Formats a date string into DD/MM/YYYY format.
 * @param dateStr The date string to format.
 * @returns Formatted date string or 'N/A' if input is null/undefined.
 */
export const formatDateForOrderDisplay = (dateStr: string | null | undefined): string => {
  if (!dateStr) return 'N/A';
  const d = new Date(dateStr);
  // Ensure month and day are two digits
  const day = d.getDate().toString().padStart(2, '0');
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const year = d.getFullYear().toString();
  return `${day}/${month}/${year}`;
};

/**
 * Gets the human-readable label for an order status.
 * @param statusValue The status value.
 * @returns The status label or the value itself (or 'N/A') if not found.
 */
export const getOrderStatusLabel = (statusValue: Order['status']): string => {
  const statusOption = STATUS_OPTIONS.find(option => option.value === statusValue);
  return statusOption ? statusOption.label : (statusValue || 'N/A');
};

/**
 * Gets the human-readable label for an order payment status.
 * @param paymentStatusValue The payment status value.
 * @returns The payment status label or the value itself (or 'N/A') if not found.
 */
export const getOrderPaymentStatusLabel = (paymentStatusValue: Order['payment_status']): string => {
  const statusOption = PAYMENT_STATUS_OPTIONS.find(option => option.value === paymentStatusValue);
  return statusOption ? statusOption.label : (paymentStatusValue || 'N/A');
};

/**
 * Gets the human-readable label for a shipping unit.
 * @param shippingUnitValue The shipping unit value.
 * @returns The shipping unit label or the value itself (or 'N/A') if not found.
 */
export const getShippingUnitLabel = (shippingUnitValue: Order['shipping_unit']): string => {
  const unitOption = SHIPPING_UNIT_OPTIONS.find(option => option.value === shippingUnitValue);
  return unitOption ? unitOption.label : (shippingUnitValue || 'N/A');
};

/**
 * Default fields selected for order export.
 */
export const DEFAULT_EXPORT_FIELDS: string[] = [
  'id',
  'created_at',
  'customer_name',
  'items_detail',
  'subtotal',
  'shipping_fee',
  'discount',
  'final_total',
  'status',
  'payment_status'
];

/**
 * Collection of toast messages used in the order export process.
 */
export const EXPORT_TOAST_MESSAGES = {
  LOADING_ALL_ORDERS: "Đang tải tất cả đơn hàng khớp lọc...",
  FETCH_LIMIT_REACHED: "Đã đạt giới hạn tải 50 trang. Nếu cần thêm, vui lòng thu hẹp bộ lọc.",
  FETCH_ALL_ORDERS_ERROR: "Lỗi khi tải tất cả đơn hàng. Vui lòng thử lại.",
  ORDERS_LOADED: (count: number) => `Đã tải ${count} đơn hàng.`,
  NO_ORDERS_FOUND_FILTER: "Không tìm thấy đơn hàng nào khớp với bộ lọc.",
  SELECT_AT_LEAST_ONE_FIELD: 'Vui lòng chọn ít nhất một trường để xuất.',
  PREPARING_EXCEL: "Đang chuẩn bị file Excel...",
  NO_ORDERS_SELECTED_FOR_EXPORT: 'Không có đơn hàng nào được chọn để xuất.',
  EXPORT_SUCCESS: 'Đã xuất danh sách đơn hàng thành công!',
  SINGLE_ORDER_EXPORT_SUCCESS: 'Đã xuất đơn hàng thành công!',
  EXPORT_ERROR: 'Có lỗi xảy ra khi xuất file. Vui lòng thử lại.',
  NO_ORDERS_MATCH_SEARCH: (searchTerm: string) => `Không tìm thấy đơn hàng nào khớp với "${searchTerm}".`,
  NO_ORDERS_TO_DISPLAY: "Không có đơn hàng nào để hiển thị.",
  NO_ORDERS_MATCH_FILTER_TO_EXPORT: "Không có đơn hàng nào khớp với bộ lọc để xuất.",
  SELECT_AT_LEAST_ONE_ORDER: "Vui lòng chọn ít nhất một đơn hàng để xuất.",
  SELECTED_ORDER_NOT_FOUND: "Không tìm thấy đơn hàng đã chọn.",
};

/**
 * Collection of button texts used in the order export page.
 */
export const EXPORT_BUTTON_TEXTS = {
  BACK_TO_ORDERS: "Quay lại danh sách đơn hàng",
  EXPORT_SELECTED: (count: number) => `Xuất (${count}) đã chọn`,
  EXPORT_ALL_FILTERED: (count: number) => `Xuất tất cả (${count} khớp lọc)`,
  SELECT_ALL_FILTERED: (count: number) => `Chọn tất cả (${count} khớp lọc)`,
  DESELECT_ALL_FILTERED: (count: number) => `Bỏ chọn tất cả (${count} khớp lọc)`,
};

/**
 * Collection of placeholder texts.
 */
export const EXPORT_PLACEHOLDERS = {
  SEARCH_ORDERS: "Tìm kiếm đơn hàng (Mã ĐH, Tên KH...)",
};

/**
 * Collection of page titles.
 */
export const EXPORT_PAGE_TITLES = {
  EXPORT_ORDERS_PAGE: "Xuất Đơn Hàng", // Changed from CSV as it exports XLSX
};

/**
 * Constants for Excel export.
 */
export const EXCEL_EXPORT = {
  MULTI_ORDER_SHEET_NAME: 'Danh sách đơn hàng',
  MULTI_ORDER_FILE_NAME_PREFIX: 'DanhSachDonHang_',
  SINGLE_ORDER_SHEET_NAME_PREFIX: 'DonHang_',
  SINGLE_ORDER_FILE_NAME_PREFIX: 'DonHang_',
  FILE_TYPE: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  DATE_FORMAT_YYYYMMDD: 'yyyy-mm-dd', // For filenames if needed
  CURRENCY_FORMAT: '#,##0 "₫"',
  PERCENTAGE_FORMAT: '0.00"%"',
  NUMBER_FORMAT: '0.00',
};

/**
 * Column definitions for the order items table in single order export.
 */
export const SINGLE_ORDER_EXPORT_ITEM_COLUMNS = ['Mã hàng', 'Sản phẩm', 'Số lượng', 'Đơn giá', 'Thành tiền'];

/**
 * Column definitions for showroom order items table in single order export.
 */
export const SINGLE_ORDER_EXPORT_SHOWROOM_ITEM_COLUMNS = ['Mã hàng', 'Sản phẩm', 'Số lượng', 'Khối lượng (kg)'];

/**
 * Summary labels for single order export.
 */
export const SINGLE_ORDER_EXPORT_SUMMARY_LABELS = {
  SUBTOTAL: 'Tạm tính:',
  SHIPPING_FEE: 'Phí giao hàng:',
  DISCOUNT: 'Giảm giá:',
  TAX_PERCENTAGE: 'Thuế (%):',
  TOTAL: 'Tổng cộng:',
};

/**
 * Export field types
 */
export type ExportType = 'multi' | 'single' | 'both';

/**
 * Export field definition with metadata
 */
export interface ExportFieldDefinition {
  label: string;
  value: string;
  exportTypes: ExportType[];
  category?: 'basic' | 'contact' | 'address' | 'product' | 'payment' | 'status' | 'staff' | 'timing' | 'other';
}

/**
 * All available export fields with metadata
 */
export const ALL_EXPORT_FIELDS: ExportFieldDefinition[] = [
  // Basic Information
  { label: 'Mã ĐH', value: 'id', exportTypes: ['both'], category: 'basic' },
  { label: 'Ngày tạo', value: 'created_at', exportTypes: ['both'], category: 'basic' },
  { label: 'Khách hàng', value: 'customer_name', exportTypes: ['both'], category: 'contact' },
  { label: 'Số điện thoại', value: 'phone_number', exportTypes: ['both'], category: 'contact' },
  { label: 'Email', value: 'email', exportTypes: ['single'], category: 'contact' },

  // Address Information
  { label: 'Địa chỉ giao hàng', value: 'shipping_address', exportTypes: ['both'], category: 'address' },
  { label: 'Phường', value: 'ward', exportTypes: ['single'], category: 'address' },
  { label: 'Quận', value: 'district', exportTypes: ['single'], category: 'address' },
  { label: 'Thành phố', value: 'city', exportTypes: ['single'], category: 'address' },

  // Product Information
  { label: 'Sản phẩm (Tóm tắt)', value: 'items_summary', exportTypes: ['single'], category: 'product' },
  { label: 'Chi tiết sản phẩm', value: 'items_detail', exportTypes: ['both'], category: 'product' },

  // Payment Information
  { label: 'Phương thức TT', value: 'payment_method', exportTypes: ['both'], category: 'payment' },
  { label: 'Tạm tính', value: 'subtotal', exportTypes: ['both'], category: 'payment' },
  { label: 'Phí giao hàng', value: 'shipping_fee', exportTypes: ['both'], category: 'payment' },
  { label: 'Giảm giá', value: 'discount', exportTypes: ['both'], category: 'payment' },
  { label: 'Thuế (%)', value: 'tax', exportTypes: ['both'], category: 'payment' },
  { label: 'Tổng tiền', value: 'final_total', exportTypes: ['both'], category: 'payment' },

  // Status Information
  { label: 'Trạng thái ĐH', value: 'status', exportTypes: ['both'], category: 'status' },
  { label: 'Trạng thái TT', value: 'payment_status', exportTypes: ['both'], category: 'status' },

  // Staff Information
  { label: 'NVBH', value: 'sales_admin', exportTypes: ['both'], category: 'staff' },
  { label: 'NV Giao hàng', value: 'delivery_staff', exportTypes: ['single'], category: 'staff' },

  // Timing Information
  { label: 'Thời gian xác nhận', value: 'confirmation_time', exportTypes: ['single'], category: 'timing' },
  { label: 'Thời gian hoàn thành', value: 'completion_time', exportTypes: ['single'], category: 'timing' },

  // Other Information
  { label: 'Ghi chú', value: 'notes', exportTypes: ['both'], category: 'other' },
  { label: 'Đơn vị vận chuyển', value: 'shipping_unit', exportTypes: ['single'], category: 'other' },
];

/**
 * Get export fields for specific export type
 */
export const getExportFieldsForType = (exportType: 'multi' | 'single') => {
  return ALL_EXPORT_FIELDS.filter(field =>
    field.exportTypes.includes(exportType) || field.exportTypes.includes('both')
  );
};

/**
 * Get export fields grouped by category for specific export type
 */
export const getExportFieldsByCategory = (exportType: 'multi' | 'single') => {
  const fields = getExportFieldsForType(exportType);
  const grouped: Record<string, ExportFieldDefinition[]> = {};

  fields.forEach(field => {
    const category = field.category || 'other';
    if (!grouped[category]) {
      grouped[category] = [];
    }
    grouped[category].push(field);
  });

  return grouped;
};

/**
 * Category display names for UI
 */
export const EXPORT_FIELD_CATEGORIES = {
  basic: 'Thông tin cơ bản',
  contact: 'Thông tin liên hệ',
  address: 'Địa chỉ',
  product: 'Sản phẩm',
  payment: 'Thanh toán',
  status: 'Trạng thái',
  staff: 'Nhân viên',
  timing: 'Thời gian',
  other: 'Khác'
};

/**
 * Available fields for multi-order export (backward compatibility)
 */
export const AVAILABLE_MULTI_ORDER_EXPORT_FIELDS = getExportFieldsForType('multi');

/**
 * Available fields for single order export (backward compatibility)
 */
export const AVAILABLE_SINGLE_ORDER_EXPORT_FIELDS = getExportFieldsForType('single');

/**
 * Multi-order export product detail format constants
 */
export const MULTI_ORDER_PRODUCT_DETAIL_FORMAT = {
  HEADER_SEPARATOR: ' | ',
  ITEM_SEPARATOR: '\n',
  FIELD_SEPARATOR: ' - ',
  CURRENCY_SYMBOL: '₫',
  QUANTITY_PREFIX: 'SL: ',
  PRICE_PREFIX: 'Giá: ',
  TOTAL_PREFIX: 'TT: ',
  CODE_PREFIX: 'Mã: ',
} as const;

/**
 * Multi-order export layout constants
 */
export const MULTI_ORDER_EXPORT_LAYOUT = {
  MAX_COLUMN_WIDTH: 80,
  MIN_COLUMN_WIDTH: 15,
  PRODUCT_DETAIL_COLUMN_WIDTH: 70,
  DEFAULT_ROW_HEIGHT: 20,
  PRODUCT_DETAIL_ROW_HEIGHT: 80,
} as const;

/**
 * Revenue table column width constants
 * Used for consistent column sizing and scroll calculation
 */
export const REVENUE_TABLE_COLUMN_WIDTHS = {
  METRIC_COLUMN: 200,    // Width for "Chỉ số" column
  PERIOD_COLUMN: 150,    // Width for each period column (Tháng/Quý/Năm)
  TOTAL_COLUMN: 180,     // Width for "Tổng" column
} as const;
