import { OrderItem } from '@/types/order';
import { formatCurrency } from '@/lib/utils';
import { MULTI_ORDER_PRODUCT_DETAIL_FORMAT } from '@/constants/constants';

/**
 * Formats order items into detailed product information for multi-order export
 * Similar to single order export but in a more compact format suitable for table cells
 */
export const formatOrderItemsDetail = (items: OrderItem[], isShowroom: boolean = false): string => {
  if (!items || items.length === 0) {
    return 'Không có sản phẩm';
  }

  const formattedItems = items.map(item => {
    const productName = item.variant_name 
      ? `${item.product_name} (${item.variant_name})` 
      : item.product_name;
    
    const productCode = item.product_code || 'N/A';
    
    if (isShowroom) {
      // For showroom orders: Code, Product, Quantity, Weight
      const weight = item.product_weight ? `${item.product_weight}kg` : 'N/A';
      return [
        `${MULTI_ORDER_PRODUCT_DETAIL_FORMAT.CODE_PREFIX}${productCode}`,
        productName,
        `${MULTI_ORDER_PRODUCT_DETAIL_FORMAT.QUANTITY_PREFIX}${item.quantity}`,
        `Khối lượng: ${weight}`
      ].join(MULTI_ORDER_PRODUCT_DETAIL_FORMAT.FIELD_SEPARATOR);
    } else {
      // For regular orders: Code, Product, Quantity, Unit Price, Total Price
      const unitPrice = formatCurrency(Number(item.price || 0));
      const totalPrice = formatCurrency(Number(item.total_price || 0));
      
      return [
        `${MULTI_ORDER_PRODUCT_DETAIL_FORMAT.CODE_PREFIX}${productCode}`,
        productName,
        `${MULTI_ORDER_PRODUCT_DETAIL_FORMAT.QUANTITY_PREFIX}${item.quantity}`,
        `${MULTI_ORDER_PRODUCT_DETAIL_FORMAT.PRICE_PREFIX}${unitPrice}`,
        `${MULTI_ORDER_PRODUCT_DETAIL_FORMAT.TOTAL_PREFIX}${totalPrice}`
      ].join(MULTI_ORDER_PRODUCT_DETAIL_FORMAT.FIELD_SEPARATOR);
    }
  });

  return formattedItems.join(MULTI_ORDER_PRODUCT_DETAIL_FORMAT.ITEM_SEPARATOR);
};

/**
 * Formats order items into a simple summary for backward compatibility
 */
export const formatOrderItemsSummary = (items: OrderItem[]): string => {
  if (!items || items.length === 0) {
    return 'Không có sản phẩm';
  }

  return items.map(item => {
    const productName = item.variant_name 
      ? `${item.product_name} (${item.variant_name})` 
      : item.product_name;
    return `${productName} x${item.quantity}`;
  }).join('; ');
};

/**
 * Calculates the optimal row height for product detail cells
 */
export const calculateProductDetailRowHeight = (items: OrderItem[]): number => {
  if (!items || items.length === 0) {
    return 20; // Default row height
  }
  
  // Base height + additional height per item
  const baseHeight = 25;
  const heightPerItem = 18;
  const maxHeight = 120;
  
  const calculatedHeight = baseHeight + (items.length * heightPerItem);
  return Math.min(calculatedHeight, maxHeight);
};

/**
 * Determines if an order has complex product details that need special formatting
 */
export const hasComplexProductDetails = (items: OrderItem[]): boolean => {
  if (!items || items.length === 0) {
    return false;
  }
  
  // Consider complex if:
  // - More than 3 items
  // - Has variants
  // - Has long product names
  return items.length > 3 || 
         items.some(item => item.variant_name) ||
         items.some(item => (item.product_name?.length || 0) > 30);
};
