import { OrderItem } from '@/types/order';
import { formatCurrency } from '@/lib/utils';
import { MULTI_ORDER_PRODUCT_DETAIL_FORMAT } from '@/constants/constants';

/**
 * Formats order items into a simple summary for backward compatibility
 */
export const formatOrderItemsSummary = (items: OrderItem[]): string => {
  if (!items || items.length === 0) {
    return 'Không có sản phẩm';
  }

  return items.map(item => {
    const productName = item.variant_name 
      ? `${item.product_name} (${item.variant_name})` 
      : item.product_name;
    return `${productName} x${item.quantity}`;
  }).join('; ');
};

/**
 * Calculates the optimal row height for product detail cells
 * Accounts for header row, separator, and data rows
 */
export const calculateProductDetailRowHeight = (items: OrderItem[]): number => {
  if (!items || items.length === 0) {
    return 20; // Default row height
  }

  // Base height for header + separator + padding
  const baseHeight = 45;
  // Height per product item
  const heightPerItem = 20;
  // Maximum height to prevent extremely tall rows
  const maxHeight = 150;

  // Calculate: header + separator + (number of items * height per item)
  const calculatedHeight = baseHeight + (items.length * heightPerItem);
  return Math.min(calculatedHeight, maxHeight);
};

/**
 * Determines if an order has complex product details that need special formatting
 */
export const hasComplexProductDetails = (items: OrderItem[]): boolean => {
  if (!items || items.length === 0) {
    return false;
  }

  // Consider complex if:
  // - More than 3 items
  // - Has variants
  // - Has long product names
  return items.length > 3 ||
         items.some(item => item.variant_name) ||
         items.some(item => (item.product_name?.length || 0) > 30);
};

/**
 * Calculates subtotal from order items
 */
export const calculateSubtotal = (items: OrderItem[]): number => {
  if (!items || items.length === 0) {
    return 0;
  }

  return items.reduce((sum, item) => {
    const itemTotal = Number(item.total_price || 0);
    return sum + itemTotal;
  }, 0);
};

/**
 * Formats tax rate as percentage for display
 */
export const formatTaxAsPercentage = (taxRate: number): number => {
  return Number(taxRate || 0) * 100;
};
