import { Worksheet } from 'exceljs';
import { Order } from '@/types/order';
import { getOrderFieldValue, createProductDetailRowData } from './dataUtils';

/**
 * Adds data rows for all orders to the worksheet
 */
export const addDataRowsToWorksheet = (
  worksheet: Worksheet,
  orders: Order[],
  selectedFields: string[]
): void => {
  orders.forEach(order => {
    const orderItems = order.items || [];
    const itemCount = Math.max(orderItems.length, 1); // At least 1 row per order
    
    // Create rows for this order (one per product item)
    for (let itemIndex = 0; itemIndex < itemCount; itemIndex++) {
      const currentItem = orderItems[itemIndex];
      const isFirstItemRow = itemIndex === 0;
      
      const rowData: any[] = [];
      
      selectedFields.forEach(fieldValue => {
        if (fieldValue === 'items_detail') {
          // Add product detail columns (always 5 columns)
          const productDetailData = createProductDetailRowData(order, currentItem);
          rowData.push(...productDetailData);
        } else {
          // Add order information only on the first row
          if (isFirstItemRow) {
            rowData.push(getOrderFieldValue(order, fieldValue));
          } else {
            // Empty cell for subsequent item rows
            rowData.push('');
          }
        }
      });
      
      worksheet.addRow(rowData);
    }
    
    // Merge cells for order information that spans multiple item rows
    if (itemCount > 1) {
      mergeCellsForOrder(worksheet, selectedFields, itemCount);
    }
  });
};

/**
 * Merges cells for order information that spans multiple item rows
 */
const mergeCellsForOrder = (
  worksheet: Worksheet,
  selectedFields: string[],
  itemCount: number
): void => {
  const currentRowNumber = worksheet.lastRow!.number;
  const startRowNumber = currentRowNumber - itemCount + 1;
  
  let colIndex = 1;
  selectedFields.forEach(fieldValue => {
    if (fieldValue !== 'items_detail') {
      // Merge this column across all item rows for this order
      worksheet.mergeCells(startRowNumber, colIndex, currentRowNumber, colIndex);
      colIndex += 1;
    } else {
      // Skip product detail columns (don't merge) - always 5 columns
      colIndex += 5;
    }
  });
};
