# Export Utilities

## Overview
This folder contains utility functions for exporting order data to Excel format.

## Key Components

### multiOrderExportUtils.ts
Utilities for formatting and exporting multiple orders to Excel with detailed product information.

#### Key Functions:

- **formatOrderItemsDetail(items, isShowroom)**: Formats order items into a table-like structure with headers and properly aligned columns
- **formatOrderItemsSummary(items)**: Creates a simple summary format for backward compatibility  
- **calculateProductDetailRowHeight(items)**: Calculates optimal row height based on number of products
- **hasComplexProductDetails(items)**: Determines if order has complex product details

#### Features:
- Table-like format with headers (Mã hàng | Sản phẩm | Số lượng | Đơn giá | Thành tiền)
- Proper column alignment using monospace font
- Text padding and truncation for consistent layout
- Support for both regular and showroom orders
- Automatic row height calculation

### singleOrderExportUtils.ts
Utilities for exporting individual orders with full detail formatting.

## Usage Example

```typescript
import { formatOrderItemsDetail } from '@/utils/multiOrderExportUtils';

// Format order items for export
const formattedItems = formatOrderItemsDetail(order.items, order.is_showroom);

// Result will be a table-like format:
// Mã hàng │Sản phẩm              │SL      │Đơn giá    │Thành tiền 
// ────────┼──────────────────────┼────────┼───────────┼───────────
// A001    │Sản phẩm A            │2       │100,000₫   │200,000₫   
// B002    │Sản phẩm B (Variant) │1       │150,000₫   │150,000₫   
```

## Constants Used

- `MULTI_ORDER_PRODUCT_DETAIL_FORMAT`: Formatting constants for separators and prefixes
- `MULTI_ORDER_EXPORT_LAYOUT`: Layout constants for column widths and row heights

## Integration

These utilities are integrated with:
- `/pages/orders/export.tsx` - Main export page
- `/constants/constants.ts` - Configuration constants
- ExcelJS library for Excel file generation
