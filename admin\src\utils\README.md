# Export Utilities

## Overview
This folder contains utility functions for exporting order data to Excel format.

## Key Components

### multiOrderExportUtils.ts
Utilities for formatting and exporting multiple orders to Excel with detailed product information.

#### Key Functions:

- **formatOrderItemsDetail(items, isShowroom)**: Formats order items into a table-like structure with headers and properly aligned columns
- **formatOrderItemsSummary(items)**: Creates a simple summary format for backward compatibility  
- **calculateProductDetailRowHeight(items)**: Calculates optimal row height based on number of products
- **hasComplexProductDetails(items)**: Determines if order has complex product details

#### Features:
- **Multi-row structure**: Each order spans multiple Excel rows (1 per product)
- **Split column headers**: "Chi tiết sản phẩm" expands into sub-columns (Mã hàng, Sản phẩm, Số lượng, Đơn giá, <PERSON>hà<PERSON> tiền)
- **Cell merging**: Order information merged across product rows
- **Proper borders and styling**: Professional table appearance
- **Support for both regular and showroom orders**
- **Automatic column width optimization**

### singleOrderExportUtils.ts
Utilities for exporting individual orders with full detail formatting.

## Excel Structure Example

```
| Mã ĐH | Khách hàng |     Chi tiết sản phẩm                    | Tổng tiền |
|       |            | Mã hàng | Tên SP | SL | Đơn giá | TT    |           |
|-------|------------|---------|--------|----|---------|----- |-----------|
| 1     | John       | A001    | SP A   | 2  | 100k    | 200k | 500k      |
|       |            | B002    | SP B   | 1  | 150k    | 150k |           |
| 2     | Jane       | C003    | SP C   | 3  | 50k     | 150k | 300k      |
```

## Usage Example

```typescript
import { formatOrderItemsSummary } from '@/utils/multiOrderExportUtils';

// The main export logic now creates actual Excel rows/columns
// instead of text formatting within cells
const formattedSummary = formatOrderItemsSummary(order.items);
// Result: "Sản phẩm A x2; Sản phẩm B x1"
```

## Constants Used

- `MULTI_ORDER_PRODUCT_DETAIL_FORMAT`: Formatting constants for separators and prefixes
- `MULTI_ORDER_EXPORT_LAYOUT`: Layout constants for column widths and row heights

## Integration

These utilities are integrated with:
- `/pages/orders/export.tsx` - Main export page
- `/constants/constants.ts` - Configuration constants
- ExcelJS library for Excel file generation
