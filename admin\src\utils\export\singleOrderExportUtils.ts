import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import { Order } from '@/types/order';
import {
  formatDateForOrderDisplay,
  getOrderPaymentStatusLabel,
  getOrderStatusLabel,
  EXCEL_EXPORT
} from '@/constants/constants';
import { formatCurrency } from '@/lib/utils';

export const exportSingleOrderToExcel = async (order: Order, selectedFields: string[]) => {
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet(`DonHang_${order.id}`);

  // Add title
  worksheet.addRow([`CHI TIẾT ĐƠN HÀNG${order.is_showroom ? " SHOWROOM ": " "}#${order.id}`]);
  worksheet.getRow(1).font = { bold: true, size: 16 };
  worksheet.addRow([]);

  // Add selected order information
  selectedFields.forEach(fieldValue => {
    const fieldData = getFieldData(order, fieldValue);
    if (fieldData) {
      worksheet.addRow([fieldData.label, fieldData.value]);
    }
  });

  // Add empty row before items
  worksheet.addRow([]);

  // Add items header
  const itemHeaders = order.is_showroom 
    ? ['Mã hàng', 'Sản phẩm', 'Số lượng', 'Khối lượng (kg)']
    : ['Mã hàng', 'Sản phẩm', 'Số lượng', 'Đơn giá', 'Thành tiền'];
  
  const headerRow = worksheet.addRow(itemHeaders);
  headerRow.font = { bold: true };

  // Add items
  order.items.forEach(item => {
    const productName = item.variant_name 
      ? `${item.product_name} (${item.variant_name})` 
      : item.product_name;

    if (order.is_showroom) {
      worksheet.addRow([
        item.product_code || 'N/A',
        productName,
        item.quantity,
        item.product_weight || 'N/A'
      ]);
    } else {
      worksheet.addRow([
        item.product_code || 'N/A',
        productName,
        item.quantity,
        item.price,
        item.total_price
      ]);
    }
  });

  // Add summary for non-showroom orders
  if (!order.is_showroom) {
    worksheet.addRow([]);
    
    const subtotal = order.items.reduce((sum, item) => sum + Number(item.total_price || 0), 0);
    const shippingFee = Number(order.shipping_fee || 0);
    const discount = Number(order.discount || 0);
    const taxRate = Number(order.tax || 0);
    
    worksheet.addRow(['Tạm tính:', '', '', '', subtotal]);
    worksheet.addRow(['Phí giao hàng:', '', '', '', shippingFee]);
    worksheet.addRow(['Giảm giá:', '', '', '', discount]);
    worksheet.addRow(['Thuế (%):', '', '', '', taxRate * 100]);
    
    const totalBeforeTax = subtotal + shippingFee - discount;
    const calculatedFinalTotal = Math.round(totalBeforeTax * (1 + taxRate));
    
    const totalRow = worksheet.addRow(['Tổng cộng:', '', '', '', calculatedFinalTotal]);
    totalRow.font = { bold: true };
  }

  // Auto-fit columns
  worksheet.columns.forEach(column => {
    let maxLength = 0;
    column.eachCell!({ includeEmpty: false }, cell => {
      const columnLength = cell.value ? cell.value.toString().length : 10;
      if (columnLength > maxLength) {
        maxLength = columnLength;
      }
    });
    column.width = maxLength < 10 ? 10 : Math.min(maxLength + 2, 50);
  });

  // Generate and download file
  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], { type: EXCEL_EXPORT.FILE_TYPE });
  const fileName = `DonHang_${order.id}_${new Date().toISOString().split('T')[0]}.xlsx`;
  saveAs(blob, fileName);
};

const getFieldData = (order: Order, fieldValue: string) => {
  switch (fieldValue) {
    case 'id':
      return { label: 'Mã đơn hàng:', value: order.id };
    case 'created_at':
      return { label: 'Ngày tạo:', value: formatDateForOrderDisplay(order.created_at) };
    case 'customer_name':
      return { label: 'Khách hàng:', value: order.user?.full_name || 'N/A' };
    case 'phone_number':
      return { label: 'Số điện thoại:', value: order.phone_number };
    case 'email':
      return { label: 'Email:', value: order.email || 'N/A' };
    case 'shipping_address':
      return { label: 'Địa chỉ:', value: `${order.shipping_address}, ${order.ward}, ${order.district}, ${order.city}` };
    case 'delivery_date':
      return order.delivery_date ? { label: 'Ngày giao:', value: formatDateForOrderDisplay(order.delivery_date) } : null;
    case 'payment_method':
      return { label: 'Phương thức thanh toán:', value: order.payment_method || 'N/A' };
    case 'payment_status':
      return { label: 'Trạng thái thanh toán:', value: getOrderPaymentStatusLabel(order.payment_status) };
    case 'status':
      return { label: 'Trạng thái đơn hàng:', value: getOrderStatusLabel(order.status) };
    case 'notes':
      return order.notes ? { label: 'Ghi chú:', value: order.notes } : null;
    default:
      return null;
  }
};
