import { Worksheet } from 'exceljs';
import { AVAILABLE_MULTI_ORDER_EXPORT_FIELDS } from '@/constants/constants';
import { ExportHeaders } from './types';

/**
 * Creates expanded headers and sub-headers for multi-order export
 */
export const createExportHeaders = (selectedFields: string[]): ExportHeaders => {
  const expandedHeaders: string[] = [];
  const subHeaders: string[] = [];
  
  selectedFields.forEach(fieldValue => {
    const field = AVAILABLE_MULTI_ORDER_EXPORT_FIELDS.find(f => f.value === fieldValue);
    const fieldLabel = field ? field.label : fieldValue;
    
    if (fieldValue === 'items_detail') {
      // Always use the full format (regular orders) to accommodate all order types
      const productColumns = ['Mã hàng', 'Sản phẩm', 'Số lượng', 'Đơn giá', 'Thành tiền'];
      
      // Add main header that will span across product columns
      expandedHeaders.push(fieldLabel);
      for (let i = 1; i < productColumns.length; i++) {
        expandedHeaders.push(''); // Empty cells for merging
      }
      
      // Add sub headers for product columns
      subHeaders.push(...productColumns);
    } else {
      expandedHeaders.push(fieldLabel);
      subHeaders.push(''); // Empty sub-header for non-product columns
    }
  });

  return { expandedHeaders, subHeaders };
};

/**
 * Adds headers to worksheet and applies styling
 */
export const addHeadersToWorksheet = (
  worksheet: Worksheet, 
  headers: ExportHeaders
): void => {
  const { expandedHeaders, subHeaders } = headers;
  
  // Add main header row
  const headerRow = worksheet.addRow(expandedHeaders);
  headerRow.font = { bold: true };
  headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
  headerRow.height = 25; // Set header row height
  
  // Add sub-header row
  const subHeaderRow = worksheet.addRow(subHeaders);
  subHeaderRow.font = { bold: true };
  subHeaderRow.alignment = { horizontal: 'center', vertical: 'middle' };
  subHeaderRow.height = 25; // Set sub-header row height
};

/**
 * Merges header cells for proper display
 */
export const mergeHeaderCells = (
  worksheet: Worksheet, 
  selectedFields: string[]
): void => {
  let currentCol = 1;
  
  selectedFields.forEach(fieldValue => {
    if (fieldValue === 'items_detail') {
      const productColumnCount = 5; // Always use 5 columns for consistency
      
      // Merge the main header across product columns
      worksheet.mergeCells(1, currentCol, 1, currentCol + productColumnCount - 1);
      currentCol += productColumnCount;
    } else {
      // Merge main header with its sub-header for non-product columns
      worksheet.mergeCells(1, currentCol, 2, currentCol);
      currentCol += 1;
    }
  });
};
