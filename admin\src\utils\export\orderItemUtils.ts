import { OrderItem } from '@/types/order';
import { formatCurrency } from '@/lib/utils';
import { MULTI_ORDER_PRODUCT_DETAIL_FORMAT } from '@/constants/constants';

/**
 * Pads text to a specific width for table alignment
 */
const padText = (text: string, width: number): string => {
  return text.length > width ? text.substring(0, width - 3) + '...' : text.padEnd(width);
};

/**
 * Formats order items into detailed product information for multi-order export
 * Creates a table-like format with headers and rows for each product
 */
export const formatOrderItemsDetail = (items: OrderItem[], isShowroom: boolean = false): string => {
  if (!items || items.length === 0) {
    return 'Không có sản phẩm';
  }

  // Define column widths for better alignment
  const columnWidths = isShowroom 
    ? { code: 8, product: 25, quantity: 8, weight: 12 }
    : { code: 8, product: 20, quantity: 8, price: 12, total: 12 };

  // Create header row with proper padding
  let headerRow: string;
  let separatorRow: string;
  
  if (isShowroom) {
    headerRow = [
      padText('Mã hàng', columnWidths.code),
      padText('Sản phẩm', columnWidths.product),
      padText('SL', columnWidths.quantity),
      padText('KL (kg)', columnWidths.weight)
    ].join('│');
    
    separatorRow = [
      '─'.repeat(columnWidths.code),
      '─'.repeat(columnWidths.product),
      '─'.repeat(columnWidths.quantity),
      '─'.repeat(columnWidths.weight)
    ].join('┼');
  } else {
    headerRow = [
      padText('Mã hàng', columnWidths.code),
      padText('Sản phẩm', columnWidths.product),
      padText('SL', columnWidths.quantity),
      padText('Đơn giá', columnWidths.price),
      padText('Thành tiền', columnWidths.total)
    ].join('│');
    
    separatorRow = [
      '─'.repeat(columnWidths.code),
      '─'.repeat(columnWidths.product),
      '─'.repeat(columnWidths.quantity),
      '─'.repeat(columnWidths.price),
      '─'.repeat(columnWidths.total)
    ].join('┼');
  }

  // Create data rows with proper padding
  const dataRows = items.map(item => {
    const productName = item.variant_name 
      ? `${item.product_name} (${item.variant_name})` 
      : item.product_name;
    
    const productCode = item.product_code || 'N/A';
    
    if (isShowroom) {
      const weight = item.product_weight ? `${item.product_weight}` : 'N/A';
      return [
        padText(productCode, columnWidths.code),
        padText(productName, columnWidths.product),
        padText(item.quantity.toString(), columnWidths.quantity),
        padText(weight, columnWidths.weight)
      ].join('│');
    } else {
      const unitPrice = formatCurrency(Number(item.price || 0));
      const totalPrice = formatCurrency(Number(item.total_price || 0));
      
      return [
        padText(productCode, columnWidths.code),
        padText(productName, columnWidths.product),
        padText(item.quantity.toString(), columnWidths.quantity),
        padText(unitPrice, columnWidths.price),
        padText(totalPrice, columnWidths.total)
      ].join('│');
    }
  });

  // Combine header, separator, and data rows
  return [
    headerRow,
    separatorRow,
    ...dataRows
  ].join(MULTI_ORDER_PRODUCT_DETAIL_FORMAT.ITEM_SEPARATOR);
};

/**
 * Formats order items into a simple summary for backward compatibility
 */
export const formatOrderItemsSummary = (items: OrderItem[]): string => {
  if (!items || items.length === 0) {
    return 'Không có sản phẩm';
  }

  return items.map(item => {
    const productName = item.variant_name 
      ? `${item.product_name} (${item.variant_name})` 
      : item.product_name;
    return `${productName} x${item.quantity}`;
  }).join('; ');
};

/**
 * Calculates the optimal row height for product detail cells
 * Accounts for header row, separator, and data rows
 */
export const calculateProductDetailRowHeight = (items: OrderItem[]): number => {
  if (!items || items.length === 0) {
    return 20; // Default row height
  }
  
  // Base height for header + separator + padding
  const baseHeight = 45;
  // Height per product item
  const heightPerItem = 20;
  // Maximum height to prevent extremely tall rows
  const maxHeight = 150;
  
  // Calculate: header + separator + (number of items * height per item)
  const calculatedHeight = baseHeight + (items.length * heightPerItem);
  return Math.min(calculatedHeight, maxHeight);
};

/**
 * Determines if an order has complex product details that need special formatting
 */
export const hasComplexProductDetails = (items: OrderItem[]): boolean => {
  if (!items || items.length === 0) {
    return false;
  }
  
  // Consider complex if:
  // - More than 3 items
  // - Has variants
  // - Has long product names
  return items.length > 3 || 
         items.some(item => item.variant_name) ||
         items.some(item => (item.product_name?.length || 0) > 30);
};

/**
 * Calculates subtotal from order items
 */
export const calculateSubtotal = (items: OrderItem[]): number => {
  if (!items || items.length === 0) {
    return 0;
  }
  
  return items.reduce((sum, item) => {
    const itemTotal = Number(item.total_price || 0);
    return sum + itemTotal;
  }, 0);
};

/**
 * Formats tax rate as percentage for display
 */
export const formatTaxAsPercentage = (taxRate: number): number => {
  return Number(taxRate || 0) * 100;
};
